# Development Docker Compose configuration for IMT Insurance Claims System

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: imt_postgres_dev
    environment:
      POSTGRES_DB: claims_db
      POSTGRES_USER: claims_user
      POSTGRES_PASSWORD: claims_password_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U claims_user -d claims_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - imt_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: imt_redis_dev
    command: redis-server --appendonly yes --requirepass redis_password_2024
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - imt_network

  # Django Web Application
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: imt_web_dev
    environment:
      - DJANGO_SETTINGS_MODULE=claims_system.settings.development
      - DEBUG=True
      - SECRET_KEY=imt_super_secret_key_for_development_2024
      - DB_ENGINE=django.db.backends.postgresql
      - DB_NAME=claims_db
      - DB_USER=claims_user
      - DB_PASSWORD=claims_password_2024
      - DB_HOST=postgres
      - DB_PORT=5432
      - REDIS_URL=redis://:redis_password_2024@redis:6379/1
      - CELERY_BROKER_URL=redis://:redis_password_2024@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:redis_password_2024@redis:6379/0
      - ALLOWED_HOSTS=demo.imtins.com,www.demo.imtins.com,localhost,127.0.0.1,web
      - SEED_DATABASE=true
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - logs_volume:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - imt_network

  # Nginx Reverse Proxy (optional for development)
  nginx:
    image: nginx:alpine
    container_name: imt_nginx_dev
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx-dev.conf:/etc/nginx/nginx.conf:ro
      - static_volume:/app/staticfiles:ro
      - media_volume:/app/media:ro
    depends_on:
      - web
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - imt_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  static_volume:
    driver: local
  media_volume:
    driver: local
  logs_volume:
    driver: local

networks:
  imt_network:
    driver: bridge
