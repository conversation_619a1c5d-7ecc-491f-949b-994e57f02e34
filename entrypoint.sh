#!/bin/bash

# IMT Insurance Claims System - Docker Entrypoint Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🏢 Starting IMT Insurance Claims System...${NC}"

# Wait for database to be ready
echo -e "${YELLOW}⏳ Waiting for database...${NC}"
until python manage.py check --database default > /dev/null 2>&1; do
    echo -e "${YELLOW}Database not ready, waiting...${NC}"
    sleep 2
done
echo -e "${GREEN}✅ Database is ready!${NC}"

# Run database migrations
echo -e "${YELLOW}🔄 Running database migrations...${NC}"
python manage.py migrate --noinput

# Create superuser if it doesn't exist
echo -e "${YELLOW}👤 Creating superuser if needed...${NC}"
python manage.py shell << EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    admin_user = User.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        first_name='IMT',
        last_name='Administrator'
    )
    admin_user.user_type = 'admin'
    admin_user.save()
    print("✅ Superuser created: admin/admin123")
else:
    # Ensure existing admin has correct user_type
    admin_user = User.objects.get(username='admin')
    if admin_user.user_type != 'admin':
        admin_user.user_type = 'admin'
        admin_user.save()
        print("✅ Admin user_type corrected")
    print("ℹ️  Superuser already exists")
EOF

# Seed database with sample data if enabled and not already seeded
if [ "${SEED_DATABASE:-true}" = "true" ]; then
    echo -e "${YELLOW}🌱 Checking if database seeding is needed...${NC}"
    python manage.py shell << EOF
from django.contrib.auth import get_user_model
from claims.models import Claim
User = get_user_model()

# Check if we already have seeded data (more than just the admin user)
user_count = User.objects.count()
claim_count = Claim.objects.count()

if user_count <= 1 and claim_count == 0:
    print("🌱 Database appears empty, seeding with sample data...")
    import subprocess
    import sys
    result = subprocess.run([sys.executable, 'manage.py', 'seed_database'],
                          capture_output=True, text=True)
    if result.returncode == 0:
        print("✅ Database seeded successfully!")
        print(result.stdout)
    else:
        print("❌ Database seeding failed:")
        print(result.stderr)
else:
    print(f"ℹ️  Database already contains data ({user_count} users, {claim_count} claims)")
    print("ℹ️  Skipping database seeding")
EOF
else
    echo -e "${YELLOW}ℹ️  Database seeding disabled (SEED_DATABASE=false)${NC}"
fi

# Collect static files with production settings
echo -e "${YELLOW}📁 Collecting static files...${NC}"
python manage.py collectstatic --noinput --clear

# Create logs directory if it doesn't exist
mkdir -p /app/logs

echo -e "${GREEN}🚀 Starting application server...${NC}"

# Execute the main command
exec "$@"
